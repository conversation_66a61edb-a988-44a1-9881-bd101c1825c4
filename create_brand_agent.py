import json
import os
from typing import List, Dict, Optional
from app.openai_client import OpenAIClient
from app.web_scraper import get_screenshot_as_base64

# Placeholder for the comprehensive JSON schema
COMPREHENSIVE_BRAND_SCHEMA = {
  "metadata": {
    "brand_id": "",
    "brand_name": "",
    "tagline": None,
    "description": None,
    "mission_statement": None,
    "vision_statement": None,
    "values": [],
    "target_audience": None,
    "brand_story": None,
    "tone_of_voice": {
      "keywords": [],
      "description": None
    },
    "locales_supported": []
  },
  "logo": {
    "primary": {
      "url": None,
      "placement": None,
      "max_height_px": None,
      "min_clearspace_ratio": None,
      "description": None,
      "usage_guidelines": None
    },
    "variations": []
  },
  "color_system": {
    "primary": [],
    "secondary": [],
    "accent": [],
    "neutral": []
  },
  "typography": {
    "primary_font": {
      "family": None,
      "usage": None
    },
    "secondary_font": {
      "family": None,
      "usage": None
    },
    "other_fonts": []
  },
  "imagery_style": {
    "description": None,
    "examples": []
  },
  "brand_assets_urls": []
}

def generate_brand_json_from_data(
    brand_name: str,
    text_content: List[str],
    screenshots_base64: List[str]
) -> Dict:
    openai_client = OpenAIClient()

    # The prompt needs to instruct the Vision API to extract specific brand elements
    vision_prompt = f"""Given the following text content and visual information (screenshots) related to the brand '{brand_name}', extract comprehensive brand details. Populate the provided JSON schema with as much detail as possible. Focus on identifying:
- Brand mission, vision, values, target audience, and brand story.
- Tone of voice keywords and descriptions.
- Logo details: primary logo URL (if found), descriptions, usage guidelines, and variations.
- Color system: primary, secondary, accent, and neutral colors, including hex codes (if visible or mentioned).
- Typography: primary, secondary, and other font families and their usage.
- Imagery style: descriptions and examples (if URLs are visible).
- Any other relevant brand asset URLs.

If a piece of information is not explicitly found, set the corresponding field to null or an empty array/object as appropriate. Do NOT make up information. The JSON should strictly adhere to this schema: {json.dumps(COMPREHENSIVE_BRAND_SCHEMA, indent=2)}

Textual Content:
{chr(10).join(text_content)}

Visual Information (from screenshots):"""

    # For simplicity, we'll just use the first screenshot for the Vision API call
    # In a more advanced agent, you'd process multiple images and texts intelligently
    if screenshots_base64:
        first_screenshot = screenshots_base64[0]
        print("Sending data to OpenAI Vision API...")
        try:
            generated_json = openai_client.analyze_image_and_generate_json(first_screenshot, vision_prompt)
            return generated_json
        except Exception as e:
            print(f"Error calling Vision API: {e}")
            return {"status": "error", "message": f"Error calling Vision API: {e}"}
    else:
        return {"status": "error", "message": "No screenshots provided for analysis."}


# This part is for demonstration/testing if run directly, not for agent execution
if __name__ == '__main__':
    # This section would typically be handled by the agent's orchestration
    # For a direct run, you'd need to manually provide data
    print("This script is designed to be called by the agent with collected data.")
    print("Example usage (manual data):")
    sample_text = ["Coca-Cola is a global brand."]
    # You would replace this with an actual base64 encoded image
    sample_screenshot = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=" # A 1x1 transparent PNG
    
    # brand_data = generate_brand_json_from_data("Coca-Cola", sample_text, [sample_screenshot])
    # print(json.dumps(brand_data, indent=2))