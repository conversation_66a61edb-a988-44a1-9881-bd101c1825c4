version: '3.8'

services:
  brand-agent:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./brands:/app/brands
      - ./screenshots:/app/screenshots
    environment:
      # IMPORTANT: Replace 'YOUR_OPENAI_API_KEY' with your actual OpenAI API key
      # Or, better yet, set it in your shell environment before running docker compose
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    command: python main_agent.py
