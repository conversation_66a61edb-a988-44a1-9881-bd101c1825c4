import json
import os
from app.openai_client import OpenAIClient
from app.web_scraper import get_screenshot_as_base64
from create_brand_agent import generate_brand_json_from_data
from app.search_utils import search_brand_urls, extract_text_content, get_page_title

def run_brand_agent(brand_name: str):
    print(f"Starting brand agent for: {brand_name}")

    # Step 1: Search for brand-related URLs using Google search
    print(f"Searching for brand information about: {brand_name}")
    potential_urls = search_brand_urls(brand_name, max_results=10)

    all_text_content = []
    all_screenshots_base64 = []
    processed_urls = set()

    # Process each URL to extract text content and capture screenshots
    for url in potential_urls:
        if url in processed_urls:
            continue
        print(f"Attempting to process URL: {url}")
        try:
            # Extract text content from the webpage
            page_title = get_page_title(url)
            text_content = extract_text_content(url)

            if text_content and text_content.strip():
                content_entry = f"URL: {url}\nTitle: {page_title}\nContent: {text_content}"
                all_text_content.append(content_entry)
                print(f"Successfully extracted text content from {url}")
            else:
                print(f"No text content extracted from {url}")

            # Capture screenshot
            screenshot = get_screenshot_as_base64(url)
            if screenshot:
                all_screenshots_base64.append(screenshot)
                print(f"Successfully captured screenshot for {url}")
            else:
                print(f"Failed to capture screenshot for {url}")

            processed_urls.add(url)

        except Exception as e:
            print(f"Could not process {url}: {e}")

    if not all_screenshots_base64 and not all_text_content:
        print("No data was captured (neither screenshots nor text content). Cannot proceed with analysis.")
        return
    elif not all_screenshots_base64:
        print("Warning: No screenshots were captured, but text content is available. Proceeding with text-only analysis.")
    elif not all_text_content:
        print("Warning: No text content was extracted, but screenshots are available. Proceeding with image-only analysis.")

    # Step 2: Generate comprehensive brand JSON using collected data
    print("Generating comprehensive brand JSON...")
    brand_data = generate_brand_json_from_data(
        brand_name=brand_name,
        text_content=all_text_content,
        screenshots_base64=all_screenshots_base64
    )

    # Step 3: Save the generated JSON
    os.makedirs("brands", exist_ok=True)
    output_filename = f"brands/{brand_name.lower().replace(' ', '_')}_comprehensive.json"
    with open(output_filename, 'w') as f:
        json.dump(brand_data, f, indent=2)
    print(f"Generated JSON saved to {output_filename}")

if __name__ == '__main__':
    # Example usage:
    brand_to_analyze = "Coca-Cola"
    run_brand_agent(brand_to_analyze)
