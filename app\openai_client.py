import os
import json
from typing import List

import openai
from dotenv import load_dotenv
from tenacity import retry, wait_exponential, stop_after_attempt


class OpenAIClient:
    def __init__(self):
        load_dotenv(override=True)
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise RuntimeError("OPENAI_API_KEY env var missing")
        openai.api_key = api_key

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_image(self, prompt: str, n: int = 1) -> List[str]:
        client = openai.OpenAI()
        resp = client.images.generate(prompt=prompt, n=n, size="1024x1024")
        return [d.url for d in resp.data]

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_json(self, prompt: str) -> dict:
        client = openai.OpenAI()
        resp = client.responses.create(
            model="gpt-4o",
            instructions="You are a helpful assistant designed to output JSON.",
            input=prompt,
            text={"format": {"type": "json_object"}}
        )
        return json.loads(resp.output_text)

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def analyze_image_and_generate_json(self, base64_image: str, prompt: str) -> dict:
        client = openai.OpenAI()
        response = client.responses.create(
            model="gpt-4o",
            input=[
                {
                    "role": "user",
                    "content": [
                        {"type": "input_text", "text": prompt},
                        {
                            "type": "input_image",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                        },
                    ],
                }
            ],
            text={"format": {"type": "json_object"}},
            max_output_tokens=1000,
        )
        return json.loads(response.output_text)
