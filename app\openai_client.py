import os
import json
from typing import List

import openai
from dotenv import load_dotenv
from tenacity import retry, wait_exponential, stop_after_attempt

class OpenAIClient:
    def __init__(self):
        load_dotenv(override=True)
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise RuntimeError("OPENAI_API_KEY env var missing")
        openai.api_key = api_key

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_image(self, prompt: str, n: int = 1) -> List[str]:
        client = openai.OpenAI()
        resp = client.images.generate(prompt=prompt, n=n, size="1024x1024")
        return [d.url for d in resp.data]

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def generate_json(self, prompt: str) -> dict:
        client = openai.OpenAI()
        resp = client.responses.create(
            model="gpt-4o",
            instructions="You are a helpful assistant designed to output JSON.",
            input=prompt,
            text={"format": {"type": "json_object"}},
        )
        return json.loads(resp.output_text)

    @retry(wait=wait_exponential(multiplier=1, min=1, max=20), stop=stop_after_attempt(3))
    def analyze_image_and_generate_json(self, base64_image: str, prompt: str) -> dict:
        print(f"[DEBUG] Starting Vision API call...")
        print(f"[DEBUG] Model: gpt-4o")
        print(f"[DEBUG] Base64 image length: {len(base64_image)} characters")
        print(f"[DEBUG] Prompt length: {len(prompt)} characters")

        client = openai.OpenAI()
        print(f"[DEBUG] OpenAI client created successfully")

        # Prepare the request payload
        request_payload = {
            "model": "gpt-4o",
            "input": [
                {
                    "role": "user",
                    "content": [
                        {"type": "input_text", "text": prompt},
                        {
                            "type": "input_image",
                            "image_url": f"data:image/jpeg;base64,{base64_image}",
                        },
                    ],
                }
            ],
            "text": {"format": {"type": "json_object"}},
            "max_output_tokens": 2000,
        }

        print(f"[DEBUG] Request payload structure prepared")
        print(f"[DEBUG] Input content items: {len(request_payload['input'][0]['content'])}")
        print(f"[DEBUG] Input text length: {len(request_payload['input'][0]['content'][0]['text'])}")
        print(f"[DEBUG] Input image length: {len(request_payload['input'][0]['content'][1]['image_url'])}")
        print(f"[DEBUG] Text format: {request_payload['text']}")
        print(f"[DEBUG] Max output tokens: {request_payload['max_output_tokens']}")

        try:
            print(f"[DEBUG] Making API call to OpenAI Responses API...")
            response = client.responses.create(**request_payload)
            print(f"[DEBUG] API call successful!")
            print(f"[DEBUG] Response type: {type(response)}")
            print(f"[DEBUG] Response attributes: {dir(response)}")

            if hasattr(response, "output_text"):
                print(f"[DEBUG] Response output_text length: {len(response.output_text)}")
                print(f"[DEBUG] Response output_text preview: {response.output_text[:200]}...")
                return json.loads(response.output_text)
            else:
                print(f"[ERROR] Response object does not have 'output_text' attribute")
                print(f"[DEBUG] Full response: {response}")
                raise AttributeError("Response object missing 'output_text' attribute")

        except Exception as e:
            print(f"[ERROR] Exception during API call: {type(e).__name__}: {e}")
            print(f"[DEBUG] Exception details: {str(e)}")
            raise
